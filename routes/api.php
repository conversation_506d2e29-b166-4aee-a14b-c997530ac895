<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\WordGuessController;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// Simple test route
Route::post('/test', function (Request $request) {
    return response()->json(['message' => 'API is working!', 'data' => $request->all()]);
});

// Word guess endpoint
Route::post('/guess', [WordGuessController::class, 'submit']);
